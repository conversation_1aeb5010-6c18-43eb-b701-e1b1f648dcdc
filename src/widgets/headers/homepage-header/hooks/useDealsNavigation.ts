import { ROUTE_NAMES } from '@config/routes';
import { getRouteApi } from '@tanstack/react-router';

const routeApi = getRouteApi('/_protected/_main/deals');

export const useDealsNavigation = () => {
  const navigate = routeApi.useNavigate();

  const navigateToDeals = (e: React.MouseEvent) => {
    e.preventDefault();
    navigate({
      to: ROUTE_NAMES.deals,
      search: undefined, // Clear all search params
      replace: true,
    });
  };

  return { navigateToDeals };
};
