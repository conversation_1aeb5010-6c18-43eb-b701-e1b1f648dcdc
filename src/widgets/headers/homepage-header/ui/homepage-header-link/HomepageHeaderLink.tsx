import { DefaultLink } from '@components/DefaultLink';
import { Typography, type TypographyProps } from '@components/typography';
import { cn } from '@utils/tailwind';
import type { TFunction } from 'i18next';
import { useTranslation } from 'react-i18next';

import type { AppLanguage } from '@/shared/types';

import type { HomepageHeaderLinkProps } from '../../types';
import { ExpandableMenuItem } from '../expandable-menu-item';

interface HomepageHeaderLinkComponentProps
  extends Omit<HomepageHeaderLinkProps, 'isActive'>,
    Pick<TypographyProps, 'variant' | 'affects'> {
  className?: string;
  disabled?: boolean;
  t?: TFunction;
}

const mapMenuItems = (
  menuItems: HomepageHeaderLinkProps['menuItems'],
  language: AppLanguage,
  t?: TFunction,
) => {
  return menuItems
    ?.filter((item) => item.href)
    .map(({ href, title, subtitle }) => ({
      href: typeof href === 'string' ? href : href?.[language],
      title: t ? t(title) : title,
      ...(subtitle && { subtitle: t ? t(subtitle) : subtitle }),
    }))
    .filter(
      (item): item is { href: string; title: string; subtitle?: string } =>
        typeof item.href === 'string' && item.href.length > 0,
    );
};

export const HomepageHeaderLink = ({
  href,
  isOpenInNewTab = false, // Default value to avoid ternary
  title,
  className,
  variant,
  affects,
  isExpandable = false, // Default value for clarity
  menuItems,
  t,
  onClick,
}: HomepageHeaderLinkComponentProps) => {
  const { i18n } = useTranslation();
  // Early return pattern for better readability
  if (isExpandable) {
    const mappedMenuItems = mapMenuItems(menuItems, i18n.language, t);

    return (
      <ExpandableMenuItem
        t={t}
        className="text-neutral-700 hover:text-primary-black"
        title={title}
        menuItems={mappedMenuItems}
        isOpenInNewTab={isOpenInNewTab}
      />
    );
  }

  // Validate required props for non-expandable links
  if (!href) {
    console.warn(
      'HomepageHeaderLink: href is required for non-expandable links',
    );
    return null;
  }

  return (
    <DefaultLink
      className={cn(className)}
      href={typeof href === 'string' ? href : href?.[i18n.language]}
      target={isOpenInNewTab ? '_blank' : '_self'}
      onClick={onClick}
    >
      <Typography
        affects={affects}
        variant={variant}
        tag="span"
        className="text-unset"
      >
        {title}
      </Typography>
    </DefaultLink>
  );
};
