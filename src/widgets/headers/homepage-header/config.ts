import { LOCIZE_NAVIGATION_KEYS } from '@config/locize';
import { ROUTE_NAMES } from '@config/routes';
import {
  BUSINESS_LINKS,
  CONSUMER_LOANS_LINKS,
  CREDIT_LINE_LINKS,
  ESTO_3_LINKS,
  HIRE_PURCHASE_LINKS,
  PARTNER_SHOPS_LINKS,
  PAY_LATER_LINKS,
  ZENDESK_LINKS,
} from '@entities/homepage/config';

import type { HomepageHeaderLinkProps } from './types';

export const HOMEPAGE_DESKTOP_TOP_HEADER_LINKS = [
  {
    title: LOCIZE_NAVIGATION_KEYS.personal,
    isActive: true,
    isOpenInNewTab: true,
    href: '/',
  },
  {
    title: LOCIZE_NAVIGATION_KEYS.business,
    href: BUSINESS_LINKS,
    isActive: false,
    isOpenInNewTab: true,
  },
] as const satisfies HomepageHeaderLinkProps[];

export const HOMEPAGE_DESKTOP_BOTTOM_HEADER_LINKS = [
  {
    title: LOCIZE_NAVIGATION_KEYS.headerLink1,
    isActive: false,
    isOpenInNewTab: true,
    isExpandable: true,
    menuItems: [
      {
        title: LOCIZE_NAVIGATION_KEYS.headerLink5,
        subtitle: LOCIZE_NAVIGATION_KEYS.headerLink5Description,
        href: CREDIT_LINE_LINKS,
        isOpenInNewTab: true,
      },
      {
        title: LOCIZE_NAVIGATION_KEYS.headerLink6,
        subtitle: LOCIZE_NAVIGATION_KEYS.headerLink6Description,
        href: CONSUMER_LOANS_LINKS,
        isOpenInNewTab: true,
      },
    ],
  },
  {
    title: LOCIZE_NAVIGATION_KEYS.headerLink2,
    isActive: true,
    href: ROUTE_NAMES.deals,
  },
  {
    title: LOCIZE_NAVIGATION_KEYS.headerLink3,
    isActive: false,
    isExpandable: true,
    menuItems: [
      {
        title: LOCIZE_NAVIGATION_KEYS.headerLink7,
        href: PARTNER_SHOPS_LINKS,
        isOpenInNewTab: true,
      },
      {
        title: LOCIZE_NAVIGATION_KEYS.headerLink8,
        href: HIRE_PURCHASE_LINKS,
        isOpenInNewTab: true,
      },
      {
        title: LOCIZE_NAVIGATION_KEYS.headerLink9,
        href: ESTO_3_LINKS,
        isOpenInNewTab: true,
      },
      {
        title: LOCIZE_NAVIGATION_KEYS.headerLink10,
        href: PAY_LATER_LINKS,
        isOpenInNewTab: true,
      },
    ],
  },
  {
    title: LOCIZE_NAVIGATION_KEYS.headerLink4,
    href: ZENDESK_LINKS,
    isActive: false,
    isOpenInNewTab: true,
  },
] as const satisfies HomepageHeaderLinkProps[];

export const HOMEPAGE_MOBILE_TOP_HEADER_LINKS = [
  {
    title: LOCIZE_NAVIGATION_KEYS.headerLink5,
    href: CREDIT_LINE_LINKS,
    isOpenInNewTab: true,
  },
  {
    title: LOCIZE_NAVIGATION_KEYS.headerLink6,
    href: CONSUMER_LOANS_LINKS,
    isOpenInNewTab: false,
  },
  {
    title: LOCIZE_NAVIGATION_KEYS.headerLink7,
    href: PARTNER_SHOPS_LINKS,
    isOpenInNewTab: true,
  },
  {
    title: LOCIZE_NAVIGATION_KEYS.headerLink8,
    href: HIRE_PURCHASE_LINKS,
    isOpenInNewTab: true,
  },
  {
    title: LOCIZE_NAVIGATION_KEYS.headerLink9,
    href: ESTO_3_LINKS,
    isOpenInNewTab: true,
  },

  {
    title: LOCIZE_NAVIGATION_KEYS.headerLink10,
    href: PAY_LATER_LINKS,
    isOpenInNewTab: true,
  },
  {
    title: LOCIZE_NAVIGATION_KEYS.headerLink4,
    href: ZENDESK_LINKS,
    isOpenInNewTab: true,
  },
] as const satisfies Omit<HomepageHeaderLinkProps, 'isActive'>[];

export const HOMEPAGE_MOBILE_BOTTOM_HEADER_LINKS = [
  {
    title: LOCIZE_NAVIGATION_KEYS.business,
    href: BUSINESS_LINKS,
    isOpenInNewTab: true,
  },
] as const satisfies Omit<
  HomepageHeaderLinkProps,
  'isActive' | 'activeClassName'
>[];
