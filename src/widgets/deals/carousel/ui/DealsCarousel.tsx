import { ProgressDots } from '@components/ProgressDots';
import {
  Carousel,
  type CarouselApi,
  CarouselContent,
} from '@components/ui/carousel';
import { cn } from '@utils/tailwind';
import Autoplay from 'embla-carousel-autoplay';
import { useCallback, useEffect, useState } from 'react';

import { DEALS_CAROUSEL_SLIDES_IDS } from '../config';
import { useDealsCarouselSlides } from '../hooks';
import { DealsCarouselItem } from './DealsCarouselItem';

export const DealsCarousel = ({ className }: { className?: string }) => {
  const [api, setApi] = useState<CarouselApi>();

  const [currentSlide, setCurrentSlide] = useState(0);
  const [shownSlides, setShownSlides] = useState<Array<number>>([]);

  const slides = useDealsCarouselSlides();

  const onProgressDotClick = useCallback(
    (index: number) => () => {
      api?.scrollTo(index);
    },
    [api],
  );

  useEffect(() => {
    if (!api) {
      return;
    }

    const handleCurrentSlideSet = () => {
      setCurrentSlide(api.selectedScrollSnap());
    };

    const handleShowSlidesSet = () => {
      const prevSlide = api.previousScrollSnap();
      if (shownSlides.includes(prevSlide)) {
        return;
      }

      setShownSlides((prev) => [...prev, prevSlide]);
    };

    api.on('select', handleCurrentSlideSet);
    api.on('select', handleShowSlidesSet);

    return () => {
      api.off('select', handleCurrentSlideSet);
      api.off('select', handleShowSlidesSet);
    };
  }, [api, shownSlides]);

  return (
    <Carousel
      className={cn('h-auto w-full', className)}
      opts={{
        loop: true,
        duration: 90,
        align: 'center',
        containScroll: false,
      }}
      setApi={setApi}
      onMouseLeave={() => api?.plugins().autoplay.play()}
      plugins={[
        Autoplay({
          delay: 6000,
          stopOnMouseEnter: true,
          playOnInit: true,
          stopOnInteraction: true,
          // added to fix onMouseEnter and onMouseLeave events
          rootNode: (emblaRoot) => emblaRoot.parentElement,
        }),
      ]}
    >
      <CarouselContent isInteractive containerClassName="h-auto">
        {slides.map(({ id, imgSrc, title, ctaLabel, onCtaClick }) => (
          <DealsCarouselItem
            key={id}
            imgSrc={imgSrc}
            title={title}
            ctaLabel={ctaLabel}
            onCtaClick={onCtaClick}
            isOnlySlide={slides.length === 1 || slides.length === 2}
            shouldLoadOnRedirect={id !== DEALS_CAROUSEL_SLIDES_IDS.NEWSLETTER}
          />
        ))}
      </CarouselContent>

      {slides.length > 1 && (
        <ProgressDots
          className="mt-6 flex w-full flex-col items-center px-6"
          dotClassName="bg-neutral-600 size-2"
          currentDotClassName="!bg-neutral-200"
          current={currentSlide}
          dots={slides.length}
          onProgressDotClick={onProgressDotClick}
        />
      )}
    </Carousel>
  );
};
