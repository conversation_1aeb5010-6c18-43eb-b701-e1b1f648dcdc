import '@config/envs';
import '@lib/i18Next';
import './sentry';
import './styles/index.css';

import { Toaster } from '@components/ui/sonner';
import { useMount } from 'react-use';

import { withProviders } from '@/app/providers';
import { AppRouter } from '@/app/router/AppRouter';

const App = () => {
  useMount(() => {
    const loader = document.getElementById('initial-loader');
    if (loader) {
      loader.remove();
    }
  });
  return (
    <>
      <AppRouter />
      <Toaster />
    </>
  );
};

export default withProviders(App);
