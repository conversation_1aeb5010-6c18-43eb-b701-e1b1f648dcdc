import {
  BreadcrumbNavigation,
  type BreadcrumbNavigationItem,
} from '@components/breadcrumb-navigation';
import { Helmet } from '@components/Helmet';
import { ProductIcon } from '@components/ProductIcon';
import { Typography } from '@components/typography';
import { Skeleton } from '@components/ui/skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@components/ui/table';
import { LOCIZE_AGREEMENTS_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { ROUTE_NAMES } from '@config/routes';
import { AGREEMENT_CONFIG_BY_SCHEDULE_TYPE } from '@entities/agreements/config';
import { useToast } from '@hooks/system';
import { getRouteApi } from '@tanstack/react-router';
import { formatDate, formatNumber } from '@utils/formatters';
import { getProductByScheduleType } from '@utils/getProductByApplicationScheduleType';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useUpdateEffect } from 'react-use';

import { ApplicationScheduleType } from '@/shared/types';

import { paymentSchedulePageApi } from '../api';

const routeApi = getRouteApi(
  '/_protected/_main/agreements/schedule/$applicationId',
);

type GroupedInstallment = {
  date: string;
  totalAmount: number;
  totalPaid: number;
  installments: any[];
};

const groupInstallmentsByDate = (installments: any[]): GroupedInstallment[] => {
  const installmentsByDate = installments
    .filter(
      (installment): installment is NonNullable<typeof installment> =>
        installment !== null,
    )
    .reduce(
      (acc, installment) => {
        const dateKey = installment.due_at;
        if (!acc[dateKey]) {
          acc[dateKey] = {
            date: dateKey,
            totalAmount: 0,
            totalPaid: 0,
            installments: [],
          };
        }
        acc[dateKey].totalAmount += installment.amount ?? 0;
        acc[dateKey].totalPaid += installment.paid;
        acc[dateKey].installments.push(installment);
        return acc;
      },
      {} as Record<string, GroupedInstallment>,
    );

  return Object.values(installmentsByDate).sort(
    (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime(),
  );
};

const renderInstallmentRow = (dateGroup: GroupedInstallment, index: number) => (
  <TableRow key={`${dateGroup.date}-${index}`}>
    <TableCell className="w-[160px]">
      <Typography variant="text-s">
        {formatDate(new Date(dateGroup.date))}
      </Typography>
    </TableCell>
    <TableCell className="w-[140px]">
      <Typography variant="text-s">
        {formatNumber({
          value: dateGroup.totalAmount,
          minimumFractionDigits: 2,
        })}{' '}
        €
      </Typography>
    </TableCell>
    <TableCell className="w-[140px]">
      <Typography variant="text-s">
        {formatNumber({
          value: dateGroup.totalPaid,
          minimumFractionDigits: 2,
        })}{' '}
        €
      </Typography>
    </TableCell>
    <TableCell className="w-[200px]">
      <Typography variant="text-s">Monthly</Typography>
    </TableCell>
  </TableRow>
);

export const PaymentSchedulePage = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.agreements);
  const { applicationId } = routeApi.useParams();
  const agreementReferenceKey = applicationId;

  const navigate = routeApi.useNavigate();

  const { data, isSuccess, isError, isFetching } =
    paymentSchedulePageApi.usePaymentScheduleApplicationQuery(
      {
        referenceKey: agreementReferenceKey,
      },
      {
        select: (data) => {
          if (!data?.application_by_reference) return null;

          const application = data.application_by_reference;
          const scheduleType = application.schedule_type;

          const { title, description } =
            AGREEMENT_CONFIG_BY_SCHEDULE_TYPE[scheduleType];

          return {
            title,
            description,
            scheduleType,
            agreementDescription:
              application.merchant?.name || (description && t(description)),
            productType: getProductByScheduleType(scheduleType),
            installments: application.installments || [],
            merchant: {
              campaignName:
                application.merchant?.campaign?.converting_schedule_name,
              name: application.merchant?.name,
              logoSrc: application.merchant?.logo_path,
            },
          };
        },
      },
    );

  const { showErrorMessage } = useToast();

  const isInvalidApplication = (isSuccess && !data) || isError;

  const hirePurchaseTypes = [
    ApplicationScheduleType.ESTO_PAY,
    ApplicationScheduleType.ESTO_X,
    ApplicationScheduleType.PAY_LATER,
    ApplicationScheduleType.REGULAR,
  ];
  const isHP = data ? hirePurchaseTypes.includes(data.scheduleType) : false;

  const handleDealsClick = () => {
    navigate({
      to: ROUTE_NAMES.agreements,
    });
  };

  const handleSmallLoanClick = () => {
    navigate({
      to: ROUTE_NAMES.agreements,
      search: {
        agreementReferenceKey,
      },
    });
  };

  const breadcrumbItems: BreadcrumbNavigationItem[] = [
    {
      label: t(LOCIZE_AGREEMENTS_KEYS.pageTitle),
      onClick: handleDealsClick,
      isActive: false,
    },
    {
      label: (isHP ? data?.merchant.name : t(data?.title as string)) ?? '',
      onClick: handleSmallLoanClick,
      isActive: false,
    },
    {
      label: t(LOCIZE_AGREEMENTS_KEYS.modalPaymentSchedule),
      isActive: true,
    },
  ];

  useUpdateEffect(() => {
    if (isInvalidApplication) {
      showErrorMessage(
        isError ? 'Something went wrong' : 'Agreement not found',
      );
    }
  });

  const groupedInstallments = useMemo(() => {
    return data ? groupInstallmentsByDate(data.installments) : [];
  }, [data]);

  if (isFetching || !data) {
    return <PaymentSchedulePageSkeleton />;
  }

  if (isInvalidApplication) {
    navigate({
      to: ROUTE_NAMES.agreements,
      replace: true,
    });
    return null;
  }

  return (
    <>
      <Helmet title="Payment Schedule" />
      <div className="flex h-full w-full flex-col items-center overflow-y-auto">
        <div className="flex w-full max-w-6xl flex-col items-center px-12 py-12 pb-[7.5rem]">
          <div className="w-full pb-10">
            <BreadcrumbNavigation items={breadcrumbItems} />
          </div>

          <div className="w-full pb-10">
            <div className="flex items-center gap-4">
              <ProductIcon
                className="h-[4.5rem] w-[4.5rem]"
                merchantLogoSrc={data.merchant.logoSrc}
                productType={data.productType}
                size="large"
              />
              <div className="flex flex-col gap-2">
                <Typography variant="m" affects="semibold">
                  {data.scheduleType === ApplicationScheduleType.ESTO_X
                    ? data.merchant.campaignName
                    : t(data.title)}
                </Typography>
                {data.agreementDescription ? (
                  <Typography variant="text-l" className="text-gray-500">
                    {data.agreementDescription}
                  </Typography>
                ) : null}
              </div>
            </div>
          </div>

          <div className="w-full">
            <div className="rounded-2xl border border-neutral-200">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[160px]">
                      {t(LOCIZE_AGREEMENTS_KEYS.agreementsTableDateCell)}
                    </TableHead>
                    <TableHead className="w-[140px]">
                      {t(LOCIZE_AGREEMENTS_KEYS.agreementsTableToPayCell)}
                    </TableHead>
                    <TableHead className="w-[140px]">
                      {t(LOCIZE_AGREEMENTS_KEYS.agreementsTablePaidCell)}
                    </TableHead>
                    <TableHead className="w-[200px]">
                      {t(LOCIZE_AGREEMENTS_KEYS.agreementsTableTypeCell)}
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {groupedInstallments.map(renderInstallmentRow)}
                </TableBody>
              </Table>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

function PaymentSchedulePageSkeleton() {
  return (
    <div className="flex h-full w-full flex-col items-center overflow-y-auto">
      <div className="flex w-full max-w-6xl flex-col items-center px-12 py-12 pb-[7.5rem]">
        <div className="w-full pb-10">
          <Skeleton className="h-6 w-64" />
        </div>

        <div className="w-full pb-10">
          <div className="flex items-center gap-4">
            <Skeleton className="h-18 w-18 rounded-full" />
            <div className="flex flex-col gap-2">
              <Skeleton className="h-8 w-48" />
              <Skeleton className="h-6 w-32" />
            </div>
          </div>
        </div>

        <div className="w-full">
          <div className="rounded-2xl border border-neutral-200">
            <Skeleton className="h-64 w-full" />
          </div>
        </div>
      </div>
    </div>
  );
}
