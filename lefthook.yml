# EXAMPLE USAGE:
#
#   Refer for explanation to following link:
#   https://evilmartians.com/chronicles/lefthook-knock-your-teams-code-back-into-shape
#   https://github.com/evilmartians/lefthook/blob/master/docs/configuration.md

commit-msg:
  parallel: true
  commands:
    check-commit-msg:
      run: pnpm exec commitlint --edit

pre-push:
  parallel: true
  commands:
    lint-format:
      glob: '*.{js,ts,cjs,mjs,jsx,tsx,json,jsonc,graphql}'
      run: pnpm exec eslint . && pnpm exec prettier --check .
    ts-check:
      glob: '*.{ts,tsx}'
      run: pnpm exec tsc --noEmit

pre-commit:
  parallel: true
  commands:
    lint-format:
      skip:
        - merge
        - rebase
      glob: '*.{js,ts,cjs,mjs,jsx,tsx,json,jsonc,graphql}'
      run: pnpm exec eslint . --fix && pnpm exec prettier --write .
    ts-check:
      glob: '*.{ts,tsx}'
      run: pnpm exec tsc --noEmit
